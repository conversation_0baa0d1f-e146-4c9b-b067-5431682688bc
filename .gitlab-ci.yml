include:
  - project: 'bkf/ebkf/carwashmanager/library/ci-config-mobile'
    file: 'common.yml'
    ref: main

android_deploy_test:
  extends: .android_deploy_template
  stage: deploy_test
  variables:
    PACKAGE_NAME: "pl.bkf.serwisant"
    TRACK: "internal"
    BUILD_VERSION: "dev-$CI_COMMIT_SHORT_SHA"
    RELEASE_STATUS: "draft"
    BUILD_NUMBER_OFFSET: 100000
  when: manual

android_deploy_prod:
  extends: .android_deploy_template
  stage: deploy_prod
  variables:
    PACKAGE_NAME: "pl.bkf.serwisant"
    TRACK: "production"
    BUILD_VERSION: $CI_COMMIT_TAG
  rules:
    - if: '$CI_COMMIT_TAG'