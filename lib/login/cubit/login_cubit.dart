import 'package:flutter/material.dart';

import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:serwis_app/core/auth/auth_repository.dart';
import 'package:serwis_app/core/auth/cubit/auth_cubit.dart';
import 'package:serwis_app/core/auth/model/auth_credentials.dart';

class LoginState extends Equatable {
  final AuthCredentials? credentials;
  final bool loading;
  final dynamic error;
  final bool rememberData;
  final bool passwordVisible;
  final TextEditingController loginController;
  final TextEditingController passwordController;
  final GlobalKey<FormState> formKey;

  const LoginState({
    this.credentials,
    this.loading = false,
    this.error,
    this.rememberData = false,
    this.passwordVisible = false,
    required this.loginController,
    required this.passwordController,
    required this.formKey,
  });

  @override
  List<Object?> get props => [
        credentials,
        loading,
        error,
        rememberData,
        passwordVisible,
        loginController,
        passwordController,
        formKey,
      ];

  LoginState copyWith({
    AuthCredentials? credentials,
    bool? loading,
    dynamic error,
    bool? rememberData,
    bool? passwordVisible,
  }) {
    return LoginState(
      credentials: credentials ?? this.credentials,
      loading: loading ?? this.loading,
      error: error,
      rememberData: rememberData ?? this.rememberData,
      passwordVisible: passwordVisible ?? this.passwordVisible,
      loginController: loginController,
      passwordController: passwordController,
      formKey: formKey,
    );
  }
}

class LoginCubit extends Cubit<LoginState> {
  LoginCubit({
    required this.authRepository,
    required this.authCubit,
  }) : super(
          LoginState(
            loginController: TextEditingController(),
            passwordController: TextEditingController(),
            formKey: GlobalKey<FormState>(),
          ),
        ) {
    init();
  }

  final AuthRepository authRepository;
  final AuthCubit authCubit;

  Future<void> init() async {
    try {
      final credentials = await authRepository.getCredentials();

      if (credentials != null) {
        state.loginController.text = credentials.login;
        state.passwordController.text = credentials.password;

        emit(state.copyWith(
          credentials: credentials,
          rememberData: true,
        ));
      }
    } catch (e) {
      emit(state.copyWith(error: e));
    }
  }

  void setRememberData(bool value) {
    emit(state.copyWith(rememberData: value));
  }

  void togglePasswordVisibility() {
    emit(state.copyWith(passwordVisible: !state.passwordVisible));
  }

  String? validateField(String? value) {
    if (value == null || value.isEmpty) {
      return 'To pole jest wymagane';
    }
    return null;
  }

  Future<void> logIn() async {
    if (state.formKey.currentState!.validate()) {
      try {
        emit(state.copyWith(loading: true, error: null));

        final credentials = AuthCredentials(
          login: state.loginController.text,
          password: state.passwordController.text,
        );

        await authCubit.logIn(credentials, state.rememberData);

        // Przywróć stan podglądu hasła do domyślnej wartości (ukryte)
        emit(state.copyWith(loading: false, passwordVisible: false));

        // Jeśli "pamiętaj dane logowania" nie jest zaznaczone, wyczyść pola tekstowe
        if (!state.rememberData) {
          state.loginController.clear();
          state.passwordController.clear();
        }
      } catch (e) {
        emit(state.copyWith(loading: false, error: e));
      }
    }
  }

  @override
  Future<void> close() {
    state.loginController.dispose();
    state.passwordController.dispose();
    return super.close();
  }
}
