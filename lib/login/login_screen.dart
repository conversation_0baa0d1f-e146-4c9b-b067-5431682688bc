import 'package:flutter/material.dart';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:package_info_plus/package_info_plus.dart';

import 'package:serwis_app/core/utils/context_extensions.dart';
import 'package:serwis_app/login/cubit/login_cubit.dart';
import 'package:serwis_app/core/widgets/loading_widget.dart';

class LoginScreen extends StatelessWidget {
  const LoginScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final loginCubit = context.read<LoginCubit>();

    return BlocListener<LoginCubit, LoginState>(
      listener: (context, state) {
        if (state.error != null) {
          context.showSnackbar('Wystąpił błąd. Sprawdź dane logowania.', error: true);
        }
      },
      child: Scaffold(
        body: Stack(
          alignment: Alignment.bottomCenter,
          children: [
            Center(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(24.0),
                child: <PERSON><PERSON>uilder<LoginCubit, LoginState>(
                  builder: (context, state) {
                    return Form(
                      key: state.formKey,
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: <Widget>[
                          Text(
                            'BKF Serwisant',
                            style: Theme.of(context).textTheme.displayLarge,
                          ),
                          const SizedBox(height: 40.0),
                          TextFormField(
                            controller: state.loginController,
                            decoration: const InputDecoration(
                              hintText: 'Login',
                              prefixIcon: Icon(Icons.person),
                            ),
                            textInputAction: TextInputAction.next,
                            validator: loginCubit.validateField,
                          ),
                          const SizedBox(height: 20.0),
                          TextFormField(
                            controller: state.passwordController,
                            obscureText: !state.passwordVisible,
                            decoration: InputDecoration(
                              hintText: 'Hasło',
                              prefixIcon: const Icon(Icons.lock),
                              suffixIcon: IconButton(
                                icon: Icon(state.passwordVisible ? Icons.visibility : Icons.visibility_off),
                                onPressed: loginCubit.togglePasswordVisibility,
                              ),
                            ),
                            textInputAction: TextInputAction.done,
                            onEditingComplete: () {
                              FocusScope.of(context).unfocus();
                              if (!state.loading) {
                                loginCubit.logIn();
                              }
                            },
                            validator: loginCubit.validateField,
                          ),
                          const SizedBox(height: 20.0),
                          CheckboxListTile(
                            controlAffinity: ListTileControlAffinity.leading,
                            contentPadding: EdgeInsets.zero,
                            value: state.rememberData,
                            onChanged: (bool? value) {
                              if (value != null) {
                                loginCubit.setRememberData(value);
                              }
                            },
                            title: Text(
                              'Pamiętaj dane logowania',
                              style: Theme.of(context).textTheme.bodyLarge,
                            ),
                          ),
                          const SizedBox(height: 20.0),
                          LoadingWidget(
                            isLoading: state.loading,
                            child: ElevatedButton(
                              onPressed: !state.loading
                                  ? () async {
                                      FocusScope.of(context).unfocus();
                                      await loginCubit.logIn();
                                    }
                                  : null,
                              child: const Text(
                                'Zaloguj',
                                style: TextStyle(
                                  fontSize: 16.0,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: FutureBuilder(
                  future: PackageInfo.fromPlatform(),
                  builder: (context, snapshot) {
                    final packageInfo = snapshot.data;
                    final version = packageInfo?.version;

                    return Text(
                      version != null ? 'Wersja $version' : '',
                    );
                  }),
            ),
          ],
        ),
      ),
    );
  }
}
