import 'package:flutter/material.dart';

import 'package:serwis_app/core/models/task.dart';

class TaskStatusChip extends StatelessWidget {
  const TaskStatusChip({
    super.key,
    required this.status,
    this.isSelected = false,
    this.onTap,
    this.disableInteraction = false,
  });

  final TaskStatus status;
  final bool isSelected;
  final VoidCallback? onTap;

  /// Gdy true, chip będzie renderowany jako zwykły kontener bez interakcji
  final bool disableInteraction;

  Color get statusColor {
    switch (status) {
      case TaskStatus.pending:
        return Colors.orange;
      case TaskStatus.planned:
        return Colors.blue;
      case TaskStatus.completed:
        return Colors.green;
      case TaskStatus.notApplicable:
        return Colors.red;
      case TaskStatus.notAvailable:
        return Colors.grey;
    }
  }

  String get statusName {
    switch (status) {
      case TaskStatus.pending:
        return 'Oczekujące';
      case TaskStatus.planned:
        return 'Zaplanowane';
      case TaskStatus.completed:
        return '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>';
      case TaskStatus.notApplicable:
        return 'Nie dotyczy';
      case TaskStatus.notAvailable:
        return 'Brak informacji';
    }
  }

  @override
  Widget build(BuildContext context) {
    final chipContent = Text(
      statusName,
      style: Theme.of(context).textTheme.labelMedium?.copyWith(
            color: statusColor,
          ),
    );

    // Jeśli interakcja jest wyłączona, zwróć tylko stylizowany kontener
    if (disableInteraction) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: statusColor.withAlpha(25),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: statusColor),
        ),
        child: chipContent,
      );
    }

    // W przeciwnym razie użyj ChoiceChip z interakcją
    return IgnorePointer(
      ignoring: onTap == null,
      child: ChoiceChip(
        label: chipContent,
        selected: isSelected,
        onSelected: (_) {
          if (onTap != null) {
            onTap!();
          }
        },
        backgroundColor: statusColor.withAlpha(25),
        selectedColor: statusColor.withAlpha(25),
        disabledColor: statusColor.withAlpha(25),
        checkmarkColor: statusColor,
        side: BorderSide(color: statusColor),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        visualDensity: VisualDensity.compact,
      ),
    );
  }
}
