import 'package:flutter/material.dart';

import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:serwis_app/core/models/task.dart';
import 'package:serwis_app/core/models/visit/visit.dart';
import 'package:serwis_app/visit/cubit/visit_cubit.dart';
import 'package:serwis_app/visit/widgets/task_card.dart';

class VisitTasksList extends StatefulWidget {
  const VisitTasksList({
    super.key,
    required this.tasks,
  });

  final List<Task> tasks;

  @override
  State<VisitTasksList> createState() => _VisitTasksListState();
}

class _VisitTasksListState extends State<VisitTasksList> with SingleTickerProviderStateMixin {
  bool _isExpanded = true;
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
      value: 1.0, // Zaczynamy od rozwinięcia
    );
    _animation = CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _toggleExpanded() {
    setState(() {
      _isExpanded = !_isExpanded;
      if (_isExpanded) {
        _controller.forward();
      } else {
        _controller.reverse();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Card(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 8.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (widget.tasks.isNotEmpty)
              BlocSelector<VisitCubit, VisitState, VisitModelStatus>(
                selector: (state) => state.visit.status,
                builder: (context, visitStatus) {
                  return ListTile(
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    onTap: _toggleExpanded,
                    leading: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: colorScheme.secondaryContainer,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        '${widget.tasks.length}',
                        style: textTheme.labelLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    title: Text(
                      'Zadania do wykonania',
                      style: textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    subtitle:
                        visitStatus == VisitModelStatus.planned ? null : Text('Wybierz zadanie, aby zmienić status'),
                    trailing: RotationTransition(
                      turns: Tween(begin: 0.0, end: 0.5).animate(_controller),
                      child: const Icon(Icons.expand_less),
                    ),
                    dense: true,
                    visualDensity: VisualDensity.compact,
                    contentPadding: const EdgeInsets.only(left: 8.0, right: 8.0),
                  );
                },
              ),
            if (widget.tasks.isEmpty)
              Center(
                child: Padding(
                  padding: const EdgeInsets.all(24.0),
                  child: Column(
                    children: [
                      Icon(
                        Icons.task_alt,
                        size: 32,
                        color: colorScheme.outline,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Brak zadań do wykonania',
                        style: textTheme.bodyMedium?.copyWith(
                          color: colorScheme.outline,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
            if (widget.tasks.isNotEmpty)
              SizeTransition(
                sizeFactor: _animation,
                axisAlignment: -1.0, // Animacja od góry do dołu
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8.0),
                  child: ListView.separated(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    padding: const EdgeInsets.only(top: 4),
                    itemCount: widget.tasks.length,
                    separatorBuilder: (context, index) => const SizedBox(height: 8),
                    itemBuilder: (context, index) {
                      final task = widget.tasks[index];
                      return TaskCard(
                        task: task,
                        index: index + 1,
                      );
                    },
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
