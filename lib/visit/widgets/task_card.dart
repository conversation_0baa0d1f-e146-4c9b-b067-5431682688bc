import 'package:flutter/material.dart';

import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:serwis_app/core/models/task.dart';
import 'package:serwis_app/core/models/visit/visit.dart';
import 'package:serwis_app/visit/cubit/visit_cubit.dart';
import 'package:serwis_app/visit/screens/edit_task_status_screen.dart';
import 'package:serwis_app/visit/widgets/task_status_chip.dart';

class TaskCard extends StatelessWidget {
  const TaskCard({
    super.key,
    required this.task,
    required this.index,
  });

  final Task task;
  final int index;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    final cardBorderRadius = BorderRadius.circular(12); // Wspólny promień

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 4.0),
      child: Material(
        // Użyj Material jako bazę dla InkWell i wyglądu
        color: colorScheme.surfaceContainerLow, // Kolor tła
        borderRadius: cardBorderRadius, // Zaokrąglenie rogów dla Material
        clipBehavior: Clip.antiAlias, // Przycinaj zawartość (w tym InkWell) do kształtu
        elevation: 0, // Ustaw elewację (cień), jeśli potrzebujesz
        child: BlocSelector<VisitCubit, VisitState, VisitModelStatus>(
          selector: (state) => state.visit.status,
          builder: (context, visitStatus) {
            return InkWell(
              onTap: visitStatus == VisitModelStatus.attended
                  ? () {
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (_) => BlocProvider.value(
                            value: context.read<VisitCubit>(),
                            child: EditTaskStatusScreen(
                              task: task,
                            ),
                          ),
                        ),
                      );
                    }
                  : null,
              borderRadius: cardBorderRadius, // Dopasuj promień InkWell do Material
              // Możesz dostosować kolory efektu, jeśli domyślne są niewidoczne
              // splashColor: colorScheme.primary.withOpacity(0.1),
              // highlightColor: colorScheme.onSurface.withOpacity(0.05),
              child: Padding(
                // Użyj Padding zamiast Container, bo Material ma już kolor
                padding: const EdgeInsets.all(16.0),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    // Główna zawartość (bez zmian)
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Pierwszy wiersz: numer zadania i opis
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              // Numer zadania
                              Container(
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: colorScheme.secondaryContainer,
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: Text(
                                  '#$index',
                                  style: textTheme.labelLarge?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),

                              const SizedBox(width: 12),

                              // Opis zadania
                              Expanded(
                                child: Text(
                                  task.description ?? 'Brak opisu zadania',
                                  style: textTheme.bodyMedium,
                                ),
                              ),

                              // Ikona sugerująca możliwość kliknięcie
                              if (visitStatus == VisitModelStatus.attended)
                                Padding(
                                  padding: const EdgeInsets.only(left: 4.0),
                                  child: Icon(
                                    Icons.chevron_right,
                                    size: 24,
                                    color: colorScheme.onSurfaceVariant,
                                  ),
                                ),
                            ],
                          ),

                          Padding(
                            padding: const EdgeInsets.symmetric(vertical: 8.0),
                            child: Divider(
                              height: 12,
                              thickness: 0.5,
                              color: Theme.of(context).dividerColor, // Użyj koloru z motywu
                            ),
                          ),

                          // Drugi wiersz: rodzaj zadania i status
                          Row(
                            spacing: 8.0,
                            children: [
                              // Rodzaj zadania
                              Expanded(
                                child: Text(
                                  task.symptom ?? '',
                                  style: textTheme.bodySmall?.copyWith(
                                    color: colorScheme.onSurfaceVariant,
                                  ),
                                ),
                              ),

                              // Status - wyłączona interakcja, aby splash działał na całej karcie
                              TaskStatusChip(
                                status: task.status,
                                disableInteraction: true,
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
