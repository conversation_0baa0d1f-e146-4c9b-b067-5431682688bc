class VisitPhotoCategory {
  final String code;
  final String name;
  final bool isRequired;
  final bool allowMultiple;
  final List<VisitPhotoCategory>? subCategories;

  const VisitPhotoCategory({
    required this.code,
    required this.name,
    required this.isRequired,
    required this.allowMultiple,
    this.subCategories,
  });

  factory VisitPhotoCategory.fromMap(Map<String, dynamic> map) {
    return VisitPhotoCategory(
      code: map['code'] ?? '',
      name: map['name'] ?? '',
      isRequired: map['isRequired'] ?? false,
      allowMultiple: map['allowMultiple'] ?? false,
      subCategories: map['subCategories'] != null
          ? List<VisitPhotoCategory>.from(
              map['subCategories']?.map((x) => VisitPhotoCategory.fromMap(x)),
            )
          : null,
    );
  }
}
