import 'package:flutter/material.dart';

import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:serwis_app/core/widgets/error_dialog.dart';
import 'package:serwis_app/core/widgets/loading_widget.dart';
import 'package:serwis_app/visit/cubit/visit_photos_cubit.dart';
import 'package:serwis_app/visit/views/visit_photo_list_view.dart';

class VisitPhotosScreen extends StatelessWidget {
  const VisitPhotosScreen({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final visit = context.read<VisitPhotosCubit>().visit;

    return Scaffold(
      appBar: AppBar(
          title: ListTile(
        contentPadding: EdgeInsets.zero,
        title: Text(
          'Wizyta ${visit.incidentId}/${visit.visitNumber}',
          style: Theme.of(context).textTheme.titleLarge,
        ),
        subtitle: const Text('Zdjęcia'),
      )),
      body: Padding(
        padding: const EdgeInsets.all(20.0),
        child: BlocListener<VisitPhotosCubit, VisitPhotosState>(
          listenWhen: (previous, current) => previous.error == null && current.error != null,
          listener: (context, state) {
            if (state.error != null) {
              ErrorDialog.show(context, state.error);
            }
          },
          child: BlocSelector<VisitPhotosCubit, VisitPhotosState, VisitPhotosStatus>(
            selector: (state) => state.status,
            builder: (context, status) {
              return Column(
                children: [
                  Expanded(
                    child: status == VisitPhotosStatus.loading
                        ? const Center(
                            child: CircularProgressIndicator(),
                          )
                        : VisitPhotoListView(),
                  ),
                  LoadingWidget(
                    isLoading: status == VisitPhotosStatus.takingPhoto,
                    child: SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed: () {
                          context.read<VisitPhotosCubit>().takePhoto();
                        },
                        icon: const Icon(Icons.add_a_photo),
                        label: const Text('Nowe zdjęcie'),
                      ),
                    ),
                  ),
                ],
              );
            },
          ),
        ),
      ),
    );
  }
}
