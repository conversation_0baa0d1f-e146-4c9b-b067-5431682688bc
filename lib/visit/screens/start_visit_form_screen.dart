import 'package:flutter/material.dart';

import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:serwis_app/visit/cubit/visit_form_cubit.dart';
import 'package:serwis_app/visit/views/start_visit_form.dart';

class StartVisitFormScreen extends StatelessWidget {
  const StartVisitFormScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final visit = context.read<VisitFormCubit>().visit;

    return Scaffold(
      appBar: AppBar(
        title: ListTile(
          contentPadding: EdgeInsets.zero,
          title: Text(
            'Wizyta ${visit.incidentId}/${visit.visitNumber}',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          subtitle: const Text('Rozpoczęcie wizyty'),
        ),
      ),
      body: const StartVisitForm(),
    );
  }
}
