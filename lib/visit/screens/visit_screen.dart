import 'package:flutter/material.dart';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';

import 'package:serwis_app/core/models/visit/visit.dart';
import 'package:serwis_app/core/widgets/error_dialog.dart';
import 'package:serwis_app/core/widgets/loading_widget.dart';
import 'package:serwis_app/visit/cubit/visit_cubit.dart';
import 'package:serwis_app/visit/widgets/visit_actions_buttons.dart';
import 'package:serwis_app/visit/widgets/visit_details_card.dart';
import 'package:serwis_app/visit/widgets/visit_tasks_list.dart';

class VisitScreen extends StatelessWidget {
  const VisitScreen({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return BlocListener<VisitCubit, VisitState>(
      listener: (context, state) {
        if (state.status == VisitStatus.error) {
          ErrorDialog.show(context, state.error);
        }
      },
      child: BlocSelector<VisitCubit, VisitState, Visit>(
        selector: (state) => state.visit,
        builder: (context, visit) {
          return Scaffold(
            appBar: AppBar(
              title: ListTile(
                contentPadding: EdgeInsets.zero,
                title: Text(
                  'Wizyta ${visit.incidentId}/${visit.visitNumber}',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                subtitle: visit.summary?.visitAttendedTime != null
                    ? Text(
                        'Przybyto o: ${DateFormat('HH:mm').format(
                          visit.summary!.visitAttendedTime!,
                        )}',
                      )
                    : null,
              ),
              actions: [
                BlocSelector<VisitCubit, VisitState, VisitStatus>(
                  selector: (state) => state.status,
                  builder: (context, status) {
                    return LoadingWidget(
                      isLoading: status == VisitStatus.refreshing,
                      child: TextButton.icon(
                        onPressed: () {
                          context.read<VisitCubit>().updateVisit();
                        },
                        icon: const Icon(Icons.refresh),
                        label: const Text('Odśwież'),
                      ),
                    );
                  },
                ),
              ],
            ),
            body: Column(
              children: [
                Expanded(
                  child: Scrollbar(
                    child: SingleChildScrollView(
                      child: Column(
                        children: [
                          VisitDetailsCard(
                            visit: visit,
                          ),
                          VisitTasksList(
                            tasks: visit.tasks,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                VisitActionButtons(),
              ],
            ),
          );
        },
      ),
    );
  }
}
