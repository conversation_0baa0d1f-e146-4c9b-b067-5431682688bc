import 'package:flutter/material.dart';

import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:serwis_app/core/models/task.dart';
import 'package:serwis_app/core/models/visit/visit.dart';
import 'package:serwis_app/visit/cubit/visit_cubit.dart';
import 'package:serwis_app/visit/views/edit_task_status_view.dart';

class EditTaskStatusScreen extends StatefulWidget {
  const EditTaskStatusScreen({
    super.key,
    required this.task,
  });

  final Task task;

  @override
  State<EditTaskStatusScreen> createState() => _EditTaskStatusScreenState();
}

class _EditTaskStatusScreenState extends State<EditTaskStatusScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: ListTile(
          contentPadding: EdgeInsets.zero,
          title: BlocSelector<VisitCubit, VisitState, Visit>(
            selector: (state) => state.visit,
            builder: (context, visit) {
              return Text(
                'Wizyta ${visit.incidentId}/${visit.visitNumber}',
                style: Theme.of(context).textTheme.titleLarge,
              );
            },
          ),
          subtitle: const Text('Zmień status zadania'),
        ),
      ),
      body: EditTaskStatusView(
        task: widget.task,
      ),
    );
  }
}
