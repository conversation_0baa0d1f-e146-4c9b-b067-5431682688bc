import 'package:flutter/material.dart';

import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:serwis_app/core/cubits/dynamic_form_cubit.dart';
import 'package:serwis_app/core/widgets/dynamic_form_view.dart';
import 'package:serwis_app/core/widgets/error_container.dart';
import 'package:serwis_app/visit/cubit/visit_form_cubit.dart';

class StartVisitForm extends StatelessWidget {
  const StartVisitForm({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<VisitFormCubit, VisitFormState>(
      listener: (context, state) {
        if (state.status == VisitFormStatus.inProgress) {
          context.read<DynamicFormCubit>().initForm(state.formFields);
        } else if (state.status == VisitFormStatus.done) {
          Navigator.of(context).pop(true);
        }
      },
      builder: (context, state) {
        if (state.status == VisitFormStatus.loading) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        final isSubmitting = state.status == VisitFormStatus.submitting;

        return Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            children: [
              Text(
                'Uzupełnij dane przed rozpoczęciem wizyty',
                style: Theme.of(context).textTheme.titleLarge,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              Expanded(
                child: SingleChildScrollView(
                  child: BlocBuilder<DynamicFormCubit, DynamicFormState>(
                    builder: (context, formState) {
                      return DynamicFormView(
                        schema: formState.schema,
                        formKey: formState.formKey,
                        onFieldChanged: (name, value) {
                          context.read<DynamicFormCubit>().updateField(name, value);
                        },
                      );
                    },
                  ),
                ),
              ),
              Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  ErrorContainer(
                    error: state.error,
                    message: 'Wystąpił błąd podczas przetwarzania formularza',
                  ),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: isSubmitting
                          ? null
                          : () async {
                              final values = context.read<DynamicFormCubit>().getFormValues();
                              await context.read<VisitFormCubit>().submitForm(values);
                            },
                      iconAlignment: IconAlignment.end,
                      icon: isSubmitting
                          ? SizedBox(
                              width: 20,
                              height: 20,
                              child: const CircularProgressIndicator(),
                            )
                          : const Icon(Icons.chevron_right),
                      label: isSubmitting ? const Text('Wysyłanie...') : const Text('Dalej'),
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }
}
