import 'dart:io';

import 'package:flutter/material.dart';

import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:serwis_app/visit/cubit/visit_photos_cubit.dart';

class VisitPhotoListView extends StatelessWidget {
  const VisitPhotoListView({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: BlocSelector<VisitPhotosCubit, VisitPhotosState, File?>(
        selector: (state) => state.photo,
        builder: (context, photo) {
          return photo != null
              ? Image.file(
                  photo,
                  fit: BoxFit.cover,
                )
              : const Text('<PERSON>rak zdj<PERSON>');
        },
      ),
    );
  }
}
