import 'package:flutter/material.dart';

import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:serwis_app/core/models/task.dart';
import 'package:serwis_app/core/widgets/loading_widget.dart';
import 'package:serwis_app/visit/cubit/visit_cubit.dart';
import 'package:serwis_app/visit/widgets/task_status_chip.dart';

class EditTaskStatusView extends StatefulWidget {
  const EditTaskStatusView({
    super.key,
    required this.task,
  });

  final Task task;

  @override
  State<EditTaskStatusView> createState() => _EditTaskStatusViewState();
}

class _EditTaskStatusViewState extends State<EditTaskStatusView> {
  late TaskStatus _selectedStatus;
  final TextEditingController _commentController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _selectedStatus = widget.task.status;
    _commentController.text = widget.task.servicemanSolution ?? '';
  }

  @override
  void dispose() {
    _commentController.dispose();
    super.dispose();
  }

  bool get _isCommentRequired => _selectedStatus == TaskStatus.notApplicable;
  bool get _canConfirm => !_isCommentRequired || _commentController.text.trim().isNotEmpty;

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Padding(
      padding: const EdgeInsets.all(20.0),
      child: Column(
        spacing: 16,
        children: [
          Expanded(
            child: Scrollbar(
              thumbVisibility: true,
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Opis zadania
                    Text(
                      'Opis:',
                      style: textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      widget.task.description ?? 'Brak opisu zadania',
                      style: textTheme.bodyMedium,
                    ),

                    const SizedBox(height: 16),

                    // Symptom zadania
                    Text(
                      'Symptom:',
                      style: textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      widget.task.symptom ?? 'Brak symptomu',
                      style: textTheme.bodyMedium,
                    ),
                  ],
                ),
              ),
            ),
          ),
          Column(
            spacing: 16,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Wybór statusu
                  Text(
                    'Wybierz status:',
                    style: textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 12),

                  // Statusy do wyboru
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    spacing: 8,
                    children: [
                      // Zaplanowane
                      TaskStatusChip(
                        status: TaskStatus.planned,
                        isSelected: _selectedStatus == TaskStatus.planned,
                        onTap: () {
                          setState(() {
                            _selectedStatus = TaskStatus.planned;
                          });
                        },
                      ),

                      // Zakończone
                      TaskStatusChip(
                        status: TaskStatus.completed,
                        isSelected: _selectedStatus == TaskStatus.completed,
                        onTap: () {
                          setState(() {
                            _selectedStatus = TaskStatus.completed;
                          });
                        },
                      ),

                      // Nie dotyczy
                      TaskStatusChip(
                        status: TaskStatus.notApplicable,
                        isSelected: _selectedStatus == TaskStatus.notApplicable,
                        onTap: () {
                          setState(() {
                            _selectedStatus = TaskStatus.notApplicable;
                          });
                        },
                      ),
                    ],
                  ),
                ],
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Pole komentarza
                  Text(
                    'Komentarz:',
                    style: textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  TextField(
                    controller: _commentController,
                    maxLines: 2,
                    decoration: InputDecoration(
                      hintText:
                          _isCommentRequired ? 'Wprowadź komentarz (wymagany)' : 'Wprowadź komentarz (opcjonalny)',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      errorText: _isCommentRequired && _commentController.text.trim().isEmpty
                          ? 'Komentarz jest wymagany dla statusu "Nie dotyczy"'
                          : null,
                    ),
                    onChanged: (value) {
                      if (_isCommentRequired) {
                        setState(() {
                          // Odświeżenie stanu dla walidacji
                        });
                      }
                    },
                  ),
                ],
              ),
              BlocSelector<VisitCubit, VisitState, VisitStatus>(
                selector: (state) => state.status,
                builder: (context, visitStatus) {
                  return LoadingWidget(
                    isLoading: visitStatus == VisitStatus.loading,
                    child: SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed: _canConfirm
                            ? () async {
                                await context.read<VisitCubit>().onTaskStatusChanged(
                                      widget.task,
                                      _selectedStatus,
                                      _commentController.text.trim().isEmpty ? null : _commentController.text.trim(),
                                    );

                                if (context.mounted) {
                                  final visitStatus = context.read<VisitCubit>().state.status;
                                  if (visitStatus != VisitStatus.error) {
                                    Navigator.of(context).pop();
                                  }
                                }
                              }
                            : null,
                        icon: const Icon(Icons.chevron_left),
                        label: const Text('Zatwierdź'),
                      ),
                    ),
                  );
                },
              ),
            ],
          ),
        ],
      ),
    );
  }
}
