import 'dart:convert';
import 'dart:io';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:path_provider/path_provider.dart';

import 'package:serwis_app/core/models/visit/visit.dart';
import 'package:serwis_app/core/services/photo_service.dart';
import 'package:serwis_app/visit/repository/visit_repository.dart';

enum VisitPhotosStatus {
  loading,
  takingPhoto,
  ready,
}

class VisitPhotosState {
  final VisitPhotosStatus status;
  final dynamic error;
  final File? photo;

  const VisitPhotosState({
    required this.status,
    this.error,
    this.photo,
  });

  VisitPhotosState copyWith({
    VisitPhotosStatus? status,
    dynamic error,
    File? photo,
  }) {
    return VisitPhotosState(
      status: status ?? this.status,
      error: error ?? this.error,
      photo: photo ?? this.photo,
    );
  }
}

class VisitPhotosCubit extends Cubit<VisitPhotosState> {
  final PhotoService photoService;
  final VisitRepository visitRepository;
  final Visit visit;

  VisitPhotosCubit({
    required this.photoService,
    required this.visitRepository,
    required this.visit,
  }) : super(
          VisitPhotosState(
            status: VisitPhotosStatus.loading,
          ),
        );

  Future<void> loadPhotos() async {
    try {
      emit(state.copyWith(status: VisitPhotosStatus.loading));

      final list = await visitRepository.getVisitPhotos(visit.id);
      final firstItem = list.data.firstOrNull;

      final photoContent = firstItem?.thumbnailContent;
      if (photoContent != null) {
        final fileName = firstItem?.filename?.split('/').last ?? 'photo.jpg';
        final photoData = base64Decode(photoContent);

        final tempDir = await getTemporaryDirectory();
        final photoPath = '${tempDir.path}/$fileName';
        final photoFile = await File(photoPath).create();
        await photoFile.writeAsBytes(photoData);

        emit(
          state.copyWith(
            status: VisitPhotosStatus.ready,
            photo: photoFile,
          ),
        );
      }
    } catch (e) {
      emit(state.copyWith(status: VisitPhotosStatus.ready, error: e));
    }
  }

  Future<void> takePhoto() async {
    try {
      emit(state.copyWith(status: VisitPhotosStatus.takingPhoto));

      final photo = await photoService.takePhoto();

      if (photo != null) {
        await visitRepository.addVisitPhoto(visit.id, photo);
      }
      emit(state.copyWith(status: VisitPhotosStatus.ready));
    } catch (e) {
      emit(state.copyWith(status: VisitPhotosStatus.ready, error: e));
    }
  }
}
