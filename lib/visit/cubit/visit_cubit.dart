import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:serwis_app/core/models/task.dart';
import 'package:serwis_app/core/models/visit/visit.dart';
import 'package:serwis_app/home/<USER>/home_cubit.dart';
import 'package:serwis_app/visit/repository/task_repository.dart';
import 'package:serwis_app/visit/repository/visit_repository.dart';

enum VisitStatus {
  refreshing,
  loading,
  ready,
  error,
}

class VisitState {
  final VisitStatus status;
  final Visit visit;
  final dynamic error;

  const VisitState({
    required this.status,
    required this.visit,
    this.error,
  });

  VisitState copyWith({
    VisitStatus? status,
    Visit? visit,
    dynamic error,
  }) {
    return VisitState(
      status: status ?? this.status,
      visit: visit ?? this.visit,
      error: error ?? this.error,
    );
  }
}

class VisitCubit extends Cubit<VisitState> {
  final VisitState initialState;
  final VisitRepository visitRepository;
  final TaskRepository taskRepository;
  final HomeCubit homeCubit;

  VisitCubit({
    required this.initialState,
    required this.visitRepository,
    required this.taskRepository,
    required this.homeCubit,
  }) : super(initialState);

  Future<void> updateVisit({bool initial = false}) async {
    try {
      if (!initial) {
        emit(state.copyWith(status: VisitStatus.refreshing));
      }

      final updatedVisit = await visitRepository.getVisit(state.visit.id);

      emit(state.copyWith(status: VisitStatus.ready, visit: updatedVisit));

      if (!initial) {
        homeCubit.refresh();
      }
    } catch (_) {
      emit(state.copyWith(status: VisitStatus.error));
    }
  }

  Future<void> onMainButtonPressed() async {
    if (state.visit.status == VisitModelStatus.planned) {
      try {
        emit(state.copyWith(status: VisitStatus.loading));
        final updatedStatus = await visitRepository.arriveAtVisit(state.visit.id);

        emit(
          state.copyWith(
            status: VisitStatus.ready,
            visit: state.visit.copyWith(
              status: updatedStatus,
              visitAttendedTime: DateTime.now(),
            ),
          ),
        );
        updateVisit();
      } catch (e) {
        emit(state.copyWith(status: VisitStatus.error, error: e));
      }
    }
  }

  Future<void> onTaskStatusChanged(Task task, TaskStatus status, String? comment) async {
    try {
      emit(state.copyWith(status: VisitStatus.loading));
      await taskRepository.updateTaskStatus(task, status, comment);

      await updateVisit();
      emit(state.copyWith(status: VisitStatus.ready));

      homeCubit.refresh();
    } catch (e) {
      emit(state.copyWith(status: VisitStatus.error, error: e));
    }
  }

  void onStartFormSubmitted() {}
}
