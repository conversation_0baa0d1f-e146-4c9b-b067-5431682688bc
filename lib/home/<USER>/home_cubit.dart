import 'package:equatable/equatable.dart';
import 'package:hydrated_bloc/hydrated_bloc.dart';

import 'package:serwis_app/core/models/visit/visit.dart';
import 'package:serwis_app/core/models/visit/visit_list.dart';
import 'package:serwis_app/visit/repository/visit_repository.dart';

enum HomeStatus {
  loading,
  refreshing,
  ready,
  error,
}

class HomeState extends Equatable {
  final HomeStatus status;
  final VisitList? visitsData;
  final String? error;

  const HomeState({
    required this.status,
    this.visitsData,
    this.error,
  });

  HomeState copyWith({
    HomeStatus? status,
    VisitList? visitsData,
    String? error,
  }) {
    return HomeState(
      status: status ?? this.status,
      visitsData: visitsData ?? this.visitsData,
      error: error ?? this.error,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'status': status.index,
      'visitsData': visitsData?.toMap(),
      'error': error,
    };
  }

  factory HomeState.fromMap(Map<String, dynamic> map) {
    return HomeState(
      status: HomeStatus.values[map['status'] ?? 0],
      visitsData: map['visitsData'] != null ? VisitList.fromMap(map['visitsData']) : null,
      error: map['error'],
    );
  }

  @override
  List<Object?> get props => [status, visitsData, error];
}

class HomeCubit extends HydratedCubit<HomeState> {
  HomeCubit(this.visitRepository)
      : super(const HomeState(
          status: HomeStatus.loading,
        ));

  final VisitRepository visitRepository;

  void loadData() async {
    try {
      emit(state.copyWith(status: state.visitsData == null ? HomeStatus.loading : HomeStatus.refreshing));

      final visitsData = await visitRepository.getVisits(
        statusList: [VisitModelStatus.attended, VisitModelStatus.planned],
      );

      emit(state.copyWith(status: HomeStatus.ready, visitsData: visitsData));
    } on Exception catch (e) {
      emit(state.copyWith(status: HomeStatus.error, error: e.toString()));
    }
  }

  void refresh() async {
    try {
      emit(state.copyWith(status: HomeStatus.refreshing));

      final visitsData = await visitRepository.getVisits(
        statusList: [VisitModelStatus.attended, VisitModelStatus.planned],
      );

      emit(state.copyWith(status: HomeStatus.ready, visitsData: visitsData));
    } on Exception catch (e) {
      emit(state.copyWith(status: HomeStatus.error, error: e.toString()));
    }
  }

  @override
  HomeState? fromJson(Map<String, dynamic> json) {
    try {
      return HomeState.fromMap(json);
    } catch (_) {
      return null;
    }
  }

  @override
  Map<String, dynamic>? toJson(HomeState state) {
    try {
      return state.toMap();
    } catch (_) {
      return null;
    }
  }
}
