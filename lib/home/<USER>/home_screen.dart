import 'package:flutter/material.dart';

import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:serwis_app/core/auth/cubit/auth_cubit.dart';
import 'package:serwis_app/core/utils/context_extensions.dart';
import 'package:serwis_app/home/<USER>/home_cubit.dart';
import 'package:serwis_app/home/<USER>/visits_list.dart';

class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Lista wizyt'),
        actions: [
          IconButton(
            onPressed: () {
              context.read<AuthCubit>().logOut();
            },
            icon: const Icon(Icons.logout),
          ),
        ],
      ),
      body: BlocConsumer<HomeCubit, HomeState>(
        listener: (context, state) {
          if (state.status == HomeStatus.error) {
            context.showSnackbar('Wystąpił bład: ${state.error}', error: true);
          }
        },
        builder: (context, state) {
          if (state.status == HomeStatus.loading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          final visits = state.visitsData?.data;

          if (visits == null) {
            return const Center(
              child: Text('Wystąpił błąd pobierania danych'),
            );
          }

          return VisitsList(
            visits: visits,
            isRefreshing: state.status == HomeStatus.refreshing,
          );
        },
      ),
    );
  }
}
