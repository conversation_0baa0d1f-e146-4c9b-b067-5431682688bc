import 'package:flutter/material.dart';

import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:serwis_app/core/models/form_field_schema.dart';

/// Stan formularza dynamicznego
class DynamicFormState extends Equatable {
  /// Lista schematów pól formularza
  final List<FormFieldSchema> schema;

  /// Mapa wartości pól formularza
  final Map<String, dynamic> values;

  /// Błąd, jeśli wystąpił
  final dynamic error;

  /// Klucz formularza używany do walidacji
  final GlobalKey<FormState> formKey;

  const DynamicFormState({
    this.schema = const [],
    this.values = const {},
    this.error,
    required this.formKey,
  });

  @override
  List<Object?> get props => [schema, values, error, formKey];

  /// Tworzy kopię stanu z nowymi wartościami
  DynamicFormState copyWith({
    List<FormFieldSchema>? schema,
    Map<String, dynamic>? values,
    dynamic error,
    GlobalKey<FormState>? formKey,
  }) {
    return DynamicFormState(
      schema: schema ?? this.schema,
      values: values ?? this.values,
      error: error,
      formKey: formKey ?? this.formKey,
    );
  }
}

/// Cubit do zarządzania stanem formularza dynamicznego
class DynamicFormCubit extends Cubit<DynamicFormState> {
  DynamicFormCubit() : super(DynamicFormState(formKey: GlobalKey<FormState>()));

  /// Inicjalizuje formularz z podanym schematem
  void initForm(List<FormFieldSchema> schema) {
    final Map<String, dynamic> values = {};

    // Inicjalizacja wartości z schematu
    for (final field in schema) {
      values[field.name] = field.value;
    }

    emit(state.copyWith(
      schema: schema,
      values: values,
      error: null,
    ));
  }

  /// Aktualizuje wartość pola formularza
  void updateField(String name, dynamic value) {
    final newValues = Map<String, dynamic>.from(state.values);
    newValues[name] = value;

    emit(state.copyWith(values: newValues));
  }

  /// Resetuje formularz do wartości początkowych
  void resetForm() {
    final Map<String, dynamic> values = {};

    // Resetowanie wartości do początkowych z schematu
    for (final field in state.schema) {
      values[field.name] = field.value;
    }

    emit(state.copyWith(values: values));
  }

  /// Waliduje formularz i zwraca true, jeśli jest poprawny
  bool validateForm() {
    return state.formKey.currentState?.validate() ?? false;
  }

  /// Zwraca aktualne wartości formularza
  Map<String, dynamic> getFormValues() {
    return state.values;
  }

  /// Zwraca schemat formularza z zaktualizowanymi wartościami
  List<FormFieldSchema> getUpdatedSchema() {
    return state.schema.map((field) {
      return FormFieldSchema(
        name: field.name,
        label: field.label,
        type: field.type,
        required: field.required,
        value: state.values[field.name],
      );
    }).toList();
  }
}
