import 'dart:convert';

class Client {
  final int id;
  final String name;
  final int? priority;


  Client({
    required this.id,
    required this.name,
    required this.priority,
   });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'priority': priority,
    };
  }

factory Client.fromMap(Map<String, dynamic> map) {
    return Client(
      id: map['id']?.toInt() ?? 0,
      name: map['name']?.toString() ?? '',
      priority: map['priority']?.toInt() ?? 0,  
    );
  }

  String toJson() => json.encode(toMap());

  factory Client.fromJson(String source) => Client.fromMap(json.decode(source));

}
