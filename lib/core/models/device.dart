import 'dart:convert';

// import 'package:serwis_app/core/models/client.dart';

class Device {
  final int id;
  final int serialNumber;
  final DateTime? startDate;
  final DateTime? warrantyEnd;
  final String type;
  final String code;
  // final Client owner;
  final int? numberOfStands;

  Device(
      {required this.id,
      required this.serialNumber,
      this.startDate,
      this.warrantyEnd,
      required this.type,
      required this.code,
      // required this.owner,
      required this.numberOfStands});

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'serialNumber': serialNumber,
      'startDate': startDate?.toIso8601String(),
      'warrantyEnd': warrantyEnd?.toIso8601String(),
      'type': type,
      'code': code,
      // 'owner': owner,
      'numberOfStands': numberOfStands
    };
  }

  factory Device.fromMap(Map<String, dynamic> map) {
    return Device(
        id: map['id']?.toInt() ?? 0,
        serialNumber: map['serialNumber']?.toInt() ?? 0,
        startDate: map['startDate'] != null ? DateTime.parse(map['startDate']) : null,
        warrantyEnd: map['warrantyEnd'] != null ? DateTime.parse(map['warrantyEnd']) : null,
        type: map['type'] ?? '',
        code: map['code'] ?? '',
        // owner: Client.fromMap(map['owner']),
        numberOfStands: map['numberOfStands']?.toInt() ?? 0);
  }

  String toJson() => json.encode(toMap());

  factory Device.fromJson(String source) => Device.fromMap(json.decode(source));
}
