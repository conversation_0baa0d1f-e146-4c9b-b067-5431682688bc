import 'package:equatable/equatable.dart';

class FormFieldSchema extends Equatable {
  final String name;
  final String? label;
  final String type;
  final bool required;
  final dynamic value;

  const FormFieldSchema({
    required this.name,
    required this.label,
    required this.type,
    required this.required,
    required this.value,
  });

  @override
  List<Object?> get props => [name, label, type, required, value];

  factory FormFieldSchema.fromJson(Map<String, dynamic> json) {
    return FormFieldSchema(
      name: json['name'],
      label: json['label'],
      type: json['type'],
      required: json['required'],
      value: json['value'],
    );
  }
}
