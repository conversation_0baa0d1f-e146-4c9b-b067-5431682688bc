import 'dart:convert';

class VisitPhoto {
  final int id;
  final String? filename;
  final String? category;
  final String? subCategory;
  final String? thumbnailContent; // base64 encoded thumbnail
  VisitPhoto({
    required this.id,
    this.filename,
    this.category,
    this.subCategory,
    this.thumbnailContent,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'filename': filename,
      'category': category,
      'subCategory': subCategory,
      'thumbnailContent': thumbnailContent,
    };
  }

  factory VisitPhoto.fromMap(Map<String, dynamic> map) {
    return VisitPhoto(
      id: map['id']?.toInt() ?? 0,
      filename: map['filename'],
      category: map['category'],
      subCategory: map['subCategory'],
      thumbnailContent: map['thumbnailContent'],
    );
  }

  String toJson() => json.encode(toMap());

  factory VisitPhoto.fromJson(String source) => VisitPhoto.fromMap(json.decode(source));
}
