import 'dart:convert';

import 'package:serwis_app/core/models/visit/visit.dart';

class VisitList {
  final List<Visit> data;
  final int count;

  const VisitList({required this.data, required this.count});

  const VisitList.empty()
      : data = const [],
        count = 0;

  factory VisitList.fromMap(Map<String, dynamic> map) {
    return VisitList(
      data: List<Visit>.from(map['data']?.map((x) => Visit.fromMap(x))),
      count: map['count']?.toInt() ?? 0,
    );
  }

  factory VisitList.fromJson(String source) => VisitList.fromMap(json.decode(source));

  Map<String, dynamic> toMap() {
    return {
      'data': data.map((x) => x.toMap()).toList(),
      'count': count,
    };
  }

  String toJson() => json.encode(toMap());
}
