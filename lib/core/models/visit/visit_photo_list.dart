import 'dart:convert';

import 'package:serwis_app/core/models/visit/visit_photo.dart';

class VisitPhotoList {
  final List<VisitPhoto> data;
  final int count;

  const VisitPhotoList({required this.data, required this.count});

  const VisitPhotoList.empty()
      : data = const [],
        count = 0;

  factory VisitPhotoList.fromMap(Map<String, dynamic> map) {
    return VisitPhotoList(
      data: List<VisitPhoto>.from(map['data']?.map((x) => VisitPhoto.fromMap(x))),
      count: map['count']?.toInt() ?? 0,
    );
  }

  factory VisitPhotoList.fromJson(String source) => VisitPhotoList.fromMap(json.decode(source));

  Map<String, dynamic> toMap() {
    return {
      'data': data.map((x) => x.toMap()).toList(),
      'count': count,
    };
  }

  String toJson() => json.encode(toMap());
}
