import 'dart:convert';

class VisitSummary {
  final DateTime? visitAttendedTime;

  const VisitSummary({
    required this.visitAttendedTime,
  });

  factory VisitSummary.fromMap(Map<String, dynamic> map) {
    return VisitSummary(
      visitAttendedTime: map['visitAttendedTime'] != null ? DateTime.parse(map['visitAttendedTime']).toLocal() : null,
    );
  }

  factory VisitSummary.fromJson(String source) => VisitSummary.fromMap(json.decode(source));

  Map<String, dynamic> toMap() {
    return {
      'visitAttendedTime': visitAttendedTime?.toIso8601String(),
    };
  }

  String toJson() => json.encode(toMap());
}
