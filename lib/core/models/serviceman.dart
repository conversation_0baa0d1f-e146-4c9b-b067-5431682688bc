import 'dart:convert';

class Serviceman {
  final int id;
  final String jrUsername;

  const Serviceman({
    required this.id,
    required this.jrUsername,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'jrUsername': jrUsername,
    };
  }

  factory Serviceman.fromMap(Map<String, dynamic> map) {
    return Serviceman(
      id: map['id']?.toInt() ?? 0,
      jrUsername: map['jr_username'] ?? '',
    );
  }

  String toJson() => json.encode(toMap());

  factory Serviceman.fromJson(String source) => Serviceman.fromMap(json.decode(source));
}
