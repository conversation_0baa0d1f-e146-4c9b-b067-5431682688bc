class DateTimeUtils {
  static Map<DateTime, List<T>> groupByDate<T>(
    List<T> list,
    DateTime? Function(T?) getDateTime,
    int Function(T, T) compare,
  ) {
    list.sort(compare);

    final groupedList = <DateTime, List<T>>{};

    for (final item in list) {
      var date = getDateTime(item);
      if (date != null) {
        date = DateTime(date.year, date.month, date.day);
        if (!groupedList.containsKey(date)) {
          groupedList[date] = [];
        }

        groupedList[date]?.add(item);
      }
    }

    return groupedList;
  }
}
