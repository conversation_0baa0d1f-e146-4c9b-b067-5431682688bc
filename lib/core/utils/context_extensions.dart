import 'package:flutter/material.dart';

extension SnackbarExtension on BuildContext {
  void showSnackbar(String message, {bool error = false}) {
    ScaffoldMessenger.of(this).removeCurrentSnackBar();
    ScaffoldMessenger.of(this).showSnackBar(
      SnackBar(
        backgroundColor: error ? Theme.of(this).colorScheme.errorContainer : null,
        content: Text(
          message,
          style: TextStyle(color: error ? Theme.of(this).colorScheme.onErrorContainer : null),
        ),
      ),
    );
  }
}

extension NavigatorExtension on BuildContext {
  void push(Widget page) {
    Navigator.of(this).push(
      MaterialPageRoute(
        builder: (context) => page,
      ),
    );
  }
}
