import 'dart:io';

import 'package:dio/dio.dart';
import 'package:dio/io.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';

import 'package:serwis_app/core/auth/model/auth_data.dart';
import 'package:serwis_app/core/config.dart';
import 'package:serwis_app/core/logger.dart';
import 'package:serwis_app/core/services/secure_storage.dart';

final _defaultDio = Dio()
  ..options.baseUrl = Config.read(ConfigKeys.apiUrl)
  ..options.headers['Content-Type'] = 'application/json'
  ..options.headers['Accept'] = 'application/json'
  ..options.receiveTimeout = const Duration(seconds: 30)
  ..options.connectTimeout = const Duration(seconds: 30)
  ..interceptors.add(
    LogInterceptor(
      request: false,
      requestHeader: false,
      requestBody: false,
      responseHeader: false,
    ),
  );

abstract class UnauthenticatedRepository {
  UnauthenticatedRepository() {
    (api.httpClientAdapter as IOHttpClientAdapter).createHttpClient =
        () => HttpClient()..badCertificateCallback = (X509Certificate? cert, String host, int? port) => true;
  }
  final api = _defaultDio;
}

abstract class AuthenticatedRepository extends UnauthenticatedRepository {
  final String? token;

  AuthenticatedRepository({required this.token});

  @override
  Dio get api => super.api
    ..options.headers['Authorization'] = 'Bearer $token'
    ..interceptors.add(RefreshTokenInterceptor());
}

class RefreshTokenInterceptor extends Interceptor {
  final _storage = SecureStorage.instance;
  int _retryCount = 0;
  static const int _maxRetries = 3;

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    final response = err.response;
    if (response?.statusCode == 401 && _retryCount < _maxRetries) {
      _retryCount++;
      try {
        final authDataRaw = await _storage.read('auth_data');
        if (authDataRaw == null) {
          Logger.log('No auth data found');
          return handler.reject(err);
        }
        var authData = AuthData.fromJson(authDataRaw);

        final body = FormData.fromMap({
          "grant_type": 'refresh_token',
          "client_id": dotenv.env['CLIENT_ID'],
          "client_secret": dotenv.env['CLIENT_SECRET'],
          "refresh_token": authData.refresh_token,
        });

        final refreshTokenResponse = await _defaultDio.post('/token', data: body);
        authData = AuthData.fromMap(refreshTokenResponse.data);
        await _storage.write('auth_data', authData.toJson());

        final headers = Map<String, dynamic>.from(err.requestOptions.headers);
        headers['Authorization'] = 'Bearer ${authData.access_token}';

        final options = Options(
          method: err.requestOptions.method,
          headers: headers,
        );
        final response = await _defaultDio.request(
          err.requestOptions.path,
          data: err.requestOptions.data,
          queryParameters: err.requestOptions.queryParameters,
          options: options,
        );

        handler.resolve(response);
      } on DioException catch (e) {
        Logger.log(e);
        handler.reject(e);
      } finally {
        _retryCount = 0;
      }
    } else {
      handler.next(err);
    }
  }
}
