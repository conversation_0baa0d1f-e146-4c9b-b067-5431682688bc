import 'package:flutter/material.dart';

class AppTheme {
  static final ColorScheme colorScheme = ColorScheme.fromSeed(
    // ZMIANA 1: Bardziej wyrazisty kolor bazowy.
    // Colors.blue to klasyczny, profesjonalny wybór, kt<PERSON>ry doda więcej "życia"
    // niż bardzo neutralny Colors.blueGrey.
    seedColor: Colors.blue, // Poprzednio: Colors.blueGrey
    brightness: Brightness.dark,
    // ZMIANA 2: Wariant, który wprowadza więcej koloru i kontrastu.
    // 'tonalSpot' wykorzystuje seedColor jako główny akcent (spot color)
    // i buduje wokół niego bardziej nasyconą paletę tonalną.
    dynamicSchemeVariant: DynamicSchemeVariant.tonalSpot, // Poprzednio: DynamicSchemeVariant.neutral
  );

  static ThemeData get theme => ThemeData(
        colorScheme: colorScheme,
        useMaterial3: true,
        appBarTheme: AppBarTheme(
          // Sugestia: Zamiast sztywnego Colors.black, można użyć koloru z ColorScheme
          // dla lepszej integracji z resztą motywu, np. colorScheme.surfaceContainerHighest
          // lub pozwolić M3 wybrać domyślny (usuwając surfaceTintColor).
          // Na razie pozostawiam, ale warto to rozważyć dla spójności.
          surfaceTintColor: Colors.black, // Rozważ: colorScheme.surfaceTint lub usunięcie
          // Uwaga: scrolledUnderElevation: 100 to bardzo duża wartość.
          // Standardowe wartości to np. 3.0 lub 4.0.
          // Nie wpływa to bezpośrednio na kolory, ale na wygląd.
          scrolledUnderElevation: 4.0, // Zmniejszono dla bardziej standardowego wyglądu
        ),
        textTheme: TextTheme(
          displayLarge: TextStyle(
            color: colorScheme.onSurface,
            fontSize: 40.0,
            fontWeight: FontWeight.bold,
          ),
          bodyLarge: TextStyle(
            color: colorScheme.onSurface,
          ),
          // Dla mniejszych przycisków tekstowych (np. 'Czas pracy', 'Zdjęcia')
          // można zdefiniować styl dla labelSmall lub użyć TextButtonTheme,
          // jeśli domyślny kontrast będzie niewystarczający.
          // np. labelSmall: TextStyle(color: colorScheme.primary) dla większej wyrazistości.
        ),
        inputDecorationTheme: InputDecorationTheme(
          // focusColor: colorScheme.primary,
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12.0),
            borderSide: BorderSide(color: colorScheme.secondary),
          ),
          filled: true,
          // ZMIANA 3: Użycie bardziej odpowiedniego koloru tła dla pól tekstowych.
          // `surfaceContainer` lub `surfaceVariant` często lepiej pasują na ciemnym `surface`.
          fillColor: colorScheme
              .surfaceContainerHighest, // Poprzednio: colorScheme.surfaceContainerHigh. Można też spróbować surfaceVariant.
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12.0),
            borderSide: BorderSide.none,
          ),
          // Użycie onSurfaceVariant dla etykiet i ikon może poprawić hierarchię wizualną.
          labelStyle: TextStyle(color: colorScheme.onSurfaceVariant),
          hintStyle: TextStyle(
            // color: colorScheme.onSurfaceVariant.withOpacity(0.65),
            color: colorScheme.onSurfaceVariant.withValues(alpha: 170),
          ), // Lekko zwiększona alfa dla lepszej czytelności
          prefixIconColor: colorScheme.onSurfaceVariant,
          suffixIconColor: colorScheme.onSurfaceVariant,
          // floatingLabelBehavior: FloatingLabelBehavior.never,
        ),
        checkboxTheme: CheckboxThemeData(
          // ZMIANA 4: Użycie koloru primary dla zaznaczonego checkboxa dla lepszego akcentu.
          fillColor: WidgetStateProperty.resolveWith((states) {
            if (states.contains(WidgetState.selected)) {
              return colorScheme.primary;
            }
            // Można zdefiniować inny kolor dla stanu niezaznaczonego, jeśli potrzeba
            return colorScheme.surfaceContainerHighest; // Kolor tła gdy niezaznaczony
          }),
          checkColor: WidgetStateProperty.all(colorScheme.onPrimary), // Kolor "ptaszka" na tle primary
          side: BorderSide(color: colorScheme.outline), // Obramowanie checkboxa
        ),
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(
            backgroundColor: colorScheme.primaryContainer,
            foregroundColor: colorScheme.onPrimaryContainer,
            padding: const EdgeInsets.symmetric(vertical: 16.0, horizontal: 24.0),
            elevation: 4,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12.0),
            ),
          ),
        ),
        // Dla przycisku "Potwierdź przybycie" (tertiaryContainer)
        // Nowe ColorScheme powinno wygenerować bardziej wyraziste kolory.
        // Jeśli nadal będzie problem, można dodać osobny styl dla niego.
        // cardColor: colorScheme.primary,
        scaffoldBackgroundColor: colorScheme.surface,
        dividerTheme: DividerThemeData(
          // ZMIANA 5: Użycie bardziej standardowego koloru dla dividera.
          // `outline` lub `outlineVariant` są typowe dla separatorów w M3.
          color: colorScheme.outlineVariant, // Poprzednio: colorScheme.onTertiaryContainer
          thickness: 1,
        ),
        // Sugestia: Jeśli małe przyciski ("Czas pracy", "Zdjęcia", "Części")
        // nadal mają słaby kontrast, można dodać:
        // textButtonTheme: TextButtonThemeData(
        //   style: TextButton.styleFrom(
        //     foregroundColor: colorScheme.primary, // lub colorScheme.onSurfaceVariant
        //     textStyle: const TextStyle(fontSize: 12), // zachowanie rozmiaru fontu
        //   ),
        // ),
        listTileTheme: ListTileThemeData(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
          ),
        ),
        chipTheme: ChipThemeData(
          backgroundColor: colorScheme.secondaryContainer,
          labelStyle: TextStyle(color: colorScheme.onSecondaryContainer),
          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 0),
          // visualDensity: VisualDensity.compact,
        ),
        cardTheme: CardTheme(
          margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
          color: colorScheme.surfaceContainerHigh,

          // elevation: 0,
          // shape: RoundedRectangleBorder(
          //   borderRadius: BorderRadius.circular(12.0),
          // ),
        ),
      );
}
