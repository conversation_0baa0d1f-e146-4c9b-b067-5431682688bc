import 'dart:developer';

import 'package:dio/dio.dart';

import 'package:serwis_app/core/auth/model/auth_credentials.dart';
import 'package:serwis_app/core/auth/model/auth_data.dart';
import 'package:serwis_app/core/config.dart';
import 'package:serwis_app/core/repository.dart';
import 'package:serwis_app/core/services/secure_storage.dart';

class AuthRepository extends UnauthenticatedRepository {
  final _storage = SecureStorage.instance;

  static const _authDataKey = 'auth_data';
  static const _credentialsKey = 'auth_credentials';

  Future<AuthData?> getAuthData() async {
    final authDataJson = await _storage.read(_authDataKey);
    final authData = authDataJson != null ? AuthData.fromJson(authDataJson) : null;

    log('token: ${authData?.access_token}');

    return authData;
  }

  Future<AuthCredentials?> getCredentials() async {
    final credentialsJson = await _storage.read(_credentialsKey);
    final credentials = credentialsJson != null ? AuthCredentials.fromJson(credentialsJson) : null;

    return credentials;
  }

  Future<AuthData> login(
    AuthCredentials credentials,
    bool rememberCredentials,
  ) async {
    final body = FormData.fromMap({
      'username': credentials.login,
      'password': credentials.password,
      'grant_type': 'password',
      'client_id': Config.read(ConfigKeys.clientId),
      'client_secret': Config.read(ConfigKeys.clientSecret),
    });

    final response = await api.post('/token', data: body);
    final data = AuthData.fromMap(response.data);

    await _storage.write(_authDataKey, data.toJson());
    await _storage.write(_credentialsKey, rememberCredentials ? credentials.toJson() : null);

    return data;
  }

  Future<void> logout() async {
    await _storage.write(_authDataKey, null);
  }
}
