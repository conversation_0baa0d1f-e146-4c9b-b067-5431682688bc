import 'dart:convert';

class AuthCredentials {
  final String login;
  final String password;

  const AuthCredentials({
    required this.login,
    required this.password,
  });

  Map<String, dynamic> toMap() {
    return {
      'login': login,
      'password': password,
    };
  }

  factory AuthCredentials.fromMap(Map<String, dynamic> map) {
    return AuthCredentials(
      login: map['login'] ?? '',
      password: map['password'] ?? '',
    );
  }

  String toJson() => json.encode(toMap());

  factory AuthCredentials.fromJson(String source) => AuthCredentials.fromMap(json.decode(source));
}
