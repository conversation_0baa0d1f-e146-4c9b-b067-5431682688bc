import 'dart:convert';

// ignore_for_file: non_constant_identifier_names

class AuthData {
  final String access_token;
  final String refresh_token;
  final int expires_in;

  const AuthData({
    required this.access_token,
    required this.refresh_token,
    required this.expires_in,
  });

  Map<String, dynamic> toMap() {
    return {
      'access_token': access_token,
      'refresh_token': refresh_token,
      'expires_in': expires_in,
    };
  }

  factory AuthData.fromMap(Map<String, dynamic> map) {
    return AuthData(
      access_token: map['access_token'] ?? '',
      refresh_token: map['refresh_token'] ?? '',
      expires_in: map['expires_in']?.toInt() ?? 0,
    );
  }

  String toJson() => json.encode(toMap());

  factory AuthData.fromJson(String source) => AuthData.fromMap(json.decode(source));
}
