import 'dart:convert';
import 'dart:io';

import 'package:image_picker/image_picker.dart';

/// Klasa serwisu do obsługi zdjęć w aplikacji.
///
/// Serwis implementuje wzorzec Singleton i udostępnia metody do:
/// - robienia zdjęć z kamery
/// - wybierania zdjęć z galerii
/// - konwersji zdjęć na format base64
class PhotoService {
  // Prywatna statyczna instancja klasy
  static final PhotoService _instance = PhotoService._internal();

  // Prywatny konstruktor
  PhotoService._internal();

  // Metoda fabryczna zwracająca instancję
  static PhotoService get instance => _instance;

  // Instancja ImagePicker
  final _picker = ImagePicker();

  /// Robi zdjęcie z kamery urządzenia.
  ///
  /// [imageQuality] - jak<PERSON><PERSON><PERSON> (0-100)
  /// [maxWidth] - maks<PERSON><PERSON><PERSON> szeroko<PERSON> zdj<PERSON><PERSON> (opcjonalnie)
  /// [maxHeight] - maksym<PERSON>na wysoko<PERSON> zd<PERSON> (opcjonalnie)
  ///
  /// Zwraca [File] zrobionego zdjęcia lub null, jeśli operacja została anulowana.
  Future<File?> takePhoto({
    int imageQuality = 80,
    double? maxWidth,
    double? maxHeight,
  }) async {
    try {
      final pickedFile = await _picker.pickImage(
        source: ImageSource.camera,
        imageQuality: imageQuality,
        maxWidth: maxWidth,
        maxHeight: maxHeight,
      );

      if (pickedFile != null) {
        return File(pickedFile.path);
      }
      return null;
    } catch (e) {
      rethrow;
    }
  }

  /// Wybiera zdjęcie z galerii urządzenia.
  ///
  /// [imageQuality] - jakość zdjęcia (0-100)
  /// [maxWidth] - maksymalna szerokość zdjęcia (opcjonalnie)
  /// [maxHeight] - maksymalna wysokość zdjęcia (opcjonalnie)
  ///
  /// Zwraca [File] wybranego zdjęcia lub null, jeśli operacja została anulowana.
  Future<File?> pickImageFromGallery({
    int imageQuality = 80,
    double? maxWidth,
    double? maxHeight,
  }) async {
    try {
      final pickedFile = await _picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: imageQuality,
        maxWidth: maxWidth,
        maxHeight: maxHeight,
      );

      if (pickedFile != null) {
        return File(pickedFile.path);
      }
      return null;
    } catch (e) {
      rethrow;
    }
  }

  /// Konwertuje plik zdjęcia na string base64.
  ///
  /// [imageFile] - plik zdjęcia do konwersji
  ///
  /// Zwraca string zawierający dane zdjęcia w formacie base64.
  Future<String> imageToBase64(File imageFile) async {
    try {
      final bytes = await imageFile.readAsBytes();
      return base64Encode(bytes);
    } catch (e) {
      rethrow;
    }
  }

  /// Robi zdjęcie i konwertuje je na base64 w jednej operacji.
  ///
  /// [imageQuality] - jakość zdjęcia (0-100)
  /// [maxWidth] - maksymalna szerokość zdjęcia (opcjonalnie)
  /// [maxHeight] - maksymalna wysokość zdjęcia (opcjonalnie)
  ///
  /// Zwraca parę (File, String) zawierającą plik zdjęcia i jego reprezentację base64,
  /// lub null jeśli operacja została anulowana.
  Future<(File, String)?> takePhotoAndConvertToBase64({
    int imageQuality = 80,
    double? maxWidth,
    double? maxHeight,
  }) async {
    try {
      final imageFile = await takePhoto(
        imageQuality: imageQuality,
        maxWidth: maxWidth,
        maxHeight: maxHeight,
      );

      if (imageFile != null) {
        final base64Image = await imageToBase64(imageFile);
        return (imageFile, base64Image);
      }
      return null;
    } catch (e) {
      rethrow;
    }
  }
}
