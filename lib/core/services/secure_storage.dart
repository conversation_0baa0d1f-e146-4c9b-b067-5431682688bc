import 'package:flutter_secure_storage/flutter_secure_storage.dart';

class SecureStorage {
  // Prywatna statyczna instancja klasy
  static final SecureStorage _instance = SecureStorage._internal();

  // Prywatny konstruktor
  SecureStorage._internal();

  // Metoda fabryczna zwracająca instancję
  static SecureStorage get instance => _instance;

  final _storage = const FlutterSecureStorage();

  Future<String?> read(String key) async {
    return await _storage.read(key: key);
  }

  Future<void> write(String key, String? value) async {
    await _storage.write(key: key, value: value);
  }
}
