import 'package:flutter/material.dart';

class ErrorDialog extends StatelessWidget {
  final dynamic error;
  final String title;
  final String message;
  final String buttonText;

  const ErrorDialog({
    super.key,
    required this.error,
    this.title = 'Wystąpił błąd',
    this.message = 'Prosimy spró<PERSON> ponownie',
    this.buttonText = 'OK',
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(title),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(message),
          if (error != null) ...[
            const SizedBox(height: 8),
            Text(
              error?.message ?? error.toString(),
              style: Theme.of(context).textTheme.labelSmall,
            ),
          ],
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Text(buttonText),
        ),
      ],
    );
  }

  // Metoda pomocnicza do wyświetlania dialogu
  static Future<void> show(
    BuildContext context,
    dynamic error, {
    String title = '<PERSON><PERSON>t<PERSON><PERSON><PERSON> błąd',
    String message = 'Prosimy spró<PERSON> ponownie',
    String buttonText = 'OK',
  }) async {
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return ErrorDialog(
          error: error,
          title: title,
          message: message,
          buttonText: buttonText,
        );
      },
    );
  }
}
