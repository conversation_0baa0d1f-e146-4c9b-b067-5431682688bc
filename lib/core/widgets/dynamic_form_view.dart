import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:serwis_app/core/models/form_field_schema.dart';

/// Widok dynamicznego formularza generowanego na podstawie schematu.
///
/// Przyjmuje listę obiektów [FormFieldSchema] i generuje odpowiednie pola formularza.
/// Obsługuje różne typy pól: text, number, checkbox, select.
class DynamicFormView extends StatefulWidget {
  /// Lista schematów pól formularza.
  final List<FormFieldSchema> schema;

  /// Callback wywoływany przy zmianie wartości pola.
  final Function(String name, dynamic value)? onFieldChanged;

  /// Czy formularz ma być tylko do odczytu.
  final bool readOnly;

  /// Klucz formularza używany do walidacji.
  final GlobalKey<FormState>? formKey;

  const DynamicFormView({
    super.key,
    required this.schema,
    this.onFieldChanged,
    this.readOnly = false,
    this.formKey,
  });

  @override
  State<DynamicFormView> createState() => _DynamicFormViewState();
}

class _DynamicFormViewState extends State<DynamicFormView> {
  // Mapa kontrolerów dla pól tekstowych i numerycznych
  final Map<String, TextEditingController> _controllers = {};

  // Mapa wartości dla pól checkbox i select
  final Map<String, dynamic> _values = {};

  @override
  void initState() {
    super.initState();
    _initializeControllers();
  }

  @override
  void didUpdateWidget(DynamicFormView oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Aktualizacja kontrolerów, jeśli schemat się zmienił
    if (oldWidget.schema != widget.schema) {
      _disposeControllers();
      _initializeControllers();
    }
  }

  @override
  void dispose() {
    _disposeControllers();
    super.dispose();
  }

  void _disposeControllers() {
    for (final controller in _controllers.values) {
      controller.dispose();
    }
    _controllers.clear();
    _values.clear();
  }

  void _initializeControllers() {
    for (final field in widget.schema) {
      if (field.type == 'text' || field.type == 'number') {
        final controller = TextEditingController(text: field.value?.toString() ?? '');
        _controllers[field.name] = controller;
      } else if (field.type == 'checkbox') {
        _values[field.name] = field.value == 'true' || field.value == true;
      } else if (field.type == 'select') {
        _values[field.name] = field.value;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Form(
      key: widget.formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: widget.schema.map((field) => _buildFormField(field)).toList(),
      ),
    );
  }

  Widget _buildFormField(FormFieldSchema field) {
    final label = field.label ?? field.name;

    switch (field.type) {
      case 'text':
        return _buildTextField(field, label);
      case 'number':
        return _buildNumberField(field, label);
      case 'checkbox':
        return _buildCheckboxField(field, label);
      case 'select':
        return _buildSelectField(field, label);
      default:
        return _buildTextField(field, label);
    }
  }

  Widget _buildTextField(FormFieldSchema field, String label) {
    final controller = _controllers[field.name]!;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: TextFormField(
        controller: controller,
        decoration: InputDecoration(
          labelText: label,
          hintText: label,
        ),
        readOnly: widget.readOnly,
        validator: field.required ? _requiredValidator : null,
        onChanged: (value) {
          if (widget.onFieldChanged != null) {
            widget.onFieldChanged!(field.name, value);
          }
        },
      ),
    );
  }

  Widget _buildNumberField(FormFieldSchema field, String label) {
    final controller = _controllers[field.name]!;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: TextFormField(
        controller: controller,
        decoration: InputDecoration(
          labelText: label,
          hintText: label,
        ),
        keyboardType: TextInputType.number,
        inputFormatters: [
          FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')),
        ],
        readOnly: widget.readOnly,
        validator: field.required ? _requiredValidator : null,
        onChanged: (value) {
          if (widget.onFieldChanged != null) {
            // Konwersja na liczbę, jeśli to możliwe
            final numValue = double.tryParse(value);
            widget.onFieldChanged!(field.name, numValue ?? value);
          }
        },
      ),
    );
  }

  Widget _buildCheckboxField(FormFieldSchema field, String label) {
    final value = _values[field.name] ?? false;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: CheckboxListTile(
        title: Text(label),
        value: value,
        controlAffinity: ListTileControlAffinity.leading,
        contentPadding: EdgeInsets.zero,
        enabled: !widget.readOnly,
        onChanged: widget.readOnly
            ? null
            : (newValue) {
                setState(() {
                  _values[field.name] = newValue;
                });
                if (widget.onFieldChanged != null && newValue != null) {
                  widget.onFieldChanged!(field.name, newValue);
                }
              },
      ),
    );
  }

  Widget _buildSelectField(FormFieldSchema field, String label) {
    // Zakładamy, że wartość dla pola select to lista opcji
    final List<String> options = _getSelectOptions(field);
    final value = _values[field.name]?.toString() ?? '';

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: DropdownButtonFormField<String>(
        decoration: InputDecoration(
          labelText: label,
          hintText: label,
        ),
        value: options.contains(value) ? value : null,
        items: options.map((option) {
          return DropdownMenuItem<String>(
            value: option,
            child: Text(option),
          );
        }).toList(),
        validator: field.required ? _requiredValidator : null,
        onChanged: widget.readOnly
            ? null
            : (newValue) {
                if (newValue != null && widget.onFieldChanged != null) {
                  setState(() {
                    _values[field.name] = newValue;
                  });
                  widget.onFieldChanged!(field.name, newValue);
                }
              },
      ),
    );
  }

  List<String> _getSelectOptions(FormFieldSchema field) {
    // Jeśli wartość jest listą, używamy jej jako opcji
    if (field.value is List) {
      return (field.value as List).map((e) => e.toString()).toList();
    }
    // Jeśli wartość jest stringiem, zakładamy że to pojedyncza opcja
    return [field.value?.toString() ?? ''];
  }

  String? _requiredValidator(String? value) {
    if (value == null || value.isEmpty) {
      return 'To pole jest wymagane';
    }
    return null;
  }
}
