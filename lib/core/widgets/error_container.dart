import 'package:flutter/material.dart';

/// Widget do wyświetlania komunikatów o błędach
///
/// Wyświetla komunikat o błędzie w kontenerze z czerwonym tłem i białym tekstem.
/// <PERSON><PERSON><PERSON> [visible] jest ustawione na false lub [error] jest null, widget nie zajmuje miejsca.
class ErrorContainer extends StatelessWidget {
  /// Treść błędu do wyświetlenia
  final dynamic error;

  /// <PERSON><PERSON> kontener ma być widoczny
  final bool visible;

  /// Dodatkowy tekst komunikatu
  final String? message;

  /// Dodatkowy padding wewnętrzny
  final EdgeInsetsGeometry padding;

  /// Marginesy zewnętrzne
  final EdgeInsetsGeometry margin;

  /// Promień zaokrąglenia rogów
  final double borderRadius;

  const ErrorContainer({
    super.key,
    required this.error,
    this.visible = true,
    this.message,
    this.padding = const EdgeInsets.all(12.0),
    this.margin = const EdgeInsets.only(bottom: 16.0),
    this.borderRadius = 8.0,
  });

  @override
  Widget build(BuildContext context) {
    // Jeś<PERSON> nie ma błędu lub widget nie ma być widoczny, zwracamy pusty kontener
    if (error == null || !visible) {
      return const SizedBox.shrink();
    }

    final errorTheme = Theme.of(context).colorScheme;

    return Container(
      width: double.infinity,
      margin: margin,
      decoration: BoxDecoration(
        color: errorTheme.errorContainer,
        borderRadius: BorderRadius.circular(borderRadius),
      ),
      child: Padding(
        padding: padding,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            if (message != null) ...[
              Text(
                message!,
                style: TextStyle(
                  color: errorTheme.onErrorContainer,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),
            ],
            Text(
              error.toString(),
              style: TextStyle(
                color: errorTheme.onErrorContainer,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
