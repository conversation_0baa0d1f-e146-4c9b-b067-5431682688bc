import 'package:flutter_dotenv/flutter_dotenv.dart';

class ConfigException implements Exception {
  final String message;

  const ConfigException(this.message);
}

class ConfigKeys {
  static const apiUrl = 'API_URL';
  static const clientId = 'CLIENT_ID';
  static const clientSecret = 'CLIENT_SECRET';
}

class Config {
  static Future<void> init() async {
    await dotenv.load(fileName: '.env');
  }

  static String read(String key) {
    final value = dotenv.env[key];

    if (value == null) {
      throw ConfigException('Key $key not found in .env file');
    }

    return value;
  }
}
